using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class GenericDataEntryRecordEntity : EntityBaseModel
{
    public required string AcademicianUniveristyUserId { get; set; }
    public required string EntryTypeSystemId { get; set; }
    public required string AssessmentPeriodId { get; set; }
    public required string DataKey { get; set; }
    public required string DataValue { get; set; }
    public string? DataType { get; set; }
    public DateTime EnteredAt { get; set; }
    public string? EnteredByUserId { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public string? VerifiedByUserId { get; set; }
    public string? Comments { get; set; }

    // Navigation properties
    public virtual GenericDataEntryDefinitionEntity? EntryTypeDefinition { get; set; }
}
