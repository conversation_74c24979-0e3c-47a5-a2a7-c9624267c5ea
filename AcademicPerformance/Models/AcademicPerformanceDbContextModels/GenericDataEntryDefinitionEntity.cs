using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class GenericDataEntryDefinitionEntity
{
    [Key]
    [StringLength(100)]
    public required string EntryTypeSystemId { get; set; } // e.g., "LIBRARY_USAGE_DATA"
    
    [Required]
    [StringLength(250)]
    public required string Name { get; set; }
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    [StringLength(100)]
    public required string ResponsibleRole { get; set; } // e.g., "Library", "TTO", "IT"
    
    [StringLength(500)]
    public string? DataStructureHint { get; set; } // JSON schema or description of expected data structure
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public string? CreatedByUserId { get; set; }
    
    // Navigation properties
    public virtual ICollection<GenericDataEntryRecordEntity>? DataEntryRecords { get; set; }
}
