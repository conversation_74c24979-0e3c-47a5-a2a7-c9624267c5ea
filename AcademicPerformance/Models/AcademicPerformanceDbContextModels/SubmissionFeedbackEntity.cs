using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

/// <summary>
/// Submission feedback entity - PostgreSQL için
/// Epic 1 Feedback Sistemi - Gelişmiş feedback kayıtları
/// </summary>
public class SubmissionFeedbackEntity : EntityBaseModel
{
    /// <summary>
    /// İlgili submission ID'si
    /// </summary>
    [Required]
    public required string SubmissionId { get; set; }

    /// <summary>
    /// Feedback türü (Approval, Rejection, RevisionRequest)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string FeedbackType { get; set; }

    /// <summary>
    /// Feedback veren controller kullanıcı ID'si
    /// </summary>
    [Required]
    public required string ControllerUserId { get; set; }

    /// <summary>
    /// Ana feedback mesajı
    /// </summary>
    [Required]
    [StringLength(2000)]
    public required string Message { get; set; }

    /// <summary>
    /// Submission'ın feedback verildiği andaki durumu
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string SubmissionStatusAtTime { get; set; }

    /// <summary>
    /// Genel performans notu (1-10 arası, opsiyonel)
    /// </summary>
    public int? OverallRating { get; set; }

    /// <summary>
    /// Öne çıkan başarılar
    /// </summary>
    [StringLength(1000)]
    public string? Highlights { get; set; }

    /// <summary>
    /// Gelecek için öneriler
    /// </summary>
    [StringLength(1000)]
    public string? FutureSuggestions { get; set; }

    /// <summary>
    /// Revision deadline (RevisionRequest için)
    /// </summary>
    public DateTime? RevisionDeadline { get; set; }

    /// <summary>
    /// Öncelik seviyesi (Low, Medium, High)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// Akademisyen yanıtı (RevisionRequest için)
    /// </summary>
    [StringLength(2000)]
    public string? AcademicianResponse { get; set; }

    /// <summary>
    /// Akademisyen yanıt tarihi
    /// </summary>
    public DateTime? AcademicianResponseDate { get; set; }

    /// <summary>
    /// Feedback aktif mi? (soft delete için)
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Notification gönderildi mi?
    /// </summary>
    public bool NotificationSent { get; set; } = false;

    /// <summary>
    /// Notification gönderim tarihi
    /// </summary>
    public DateTime? NotificationSentAt { get; set; }

    // Audit fields - EntityBaseModel'de yok, manuel ekliyoruz
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedByUserId { get; set; }

    // Navigation properties
    public virtual AcademicSubmissionEntity? Submission { get; set; }
    public virtual ICollection<CriterionFeedbackEntity>? CriterionFeedbacks { get; set; }
}

/// <summary>
/// Kriter bazında feedback entity - PostgreSQL için
/// </summary>
public class CriterionFeedbackEntity : EntityBaseModel
{
    /// <summary>
    /// Ana feedback ID'si
    /// </summary>
    [Required]
    public required string SubmissionFeedbackId { get; set; }

    /// <summary>
    /// Kriter ID'si (FormCriterionLink ID'si)
    /// </summary>
    [Required]
    public required string CriterionLinkId { get; set; }

    /// <summary>
    /// Kriter için feedback mesajı
    /// </summary>
    [Required]
    [StringLength(1000)]
    public required string FeedbackMessage { get; set; }

    /// <summary>
    /// Kriter durumu (Approved, NeedsRevision, Rejected, Excellent, Good, Satisfactory, NeedsImprovement)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string Status { get; set; }

    /// <summary>
    /// Kriter notu (1-10 arası, opsiyonel)
    /// </summary>
    public int? Rating { get; set; }

    /// <summary>
    /// Revision türü (DataCorrection, EvidenceUpdate, AdditionalInfo)
    /// </summary>
    [StringLength(50)]
    public string? RevisionType { get; set; }

    /// <summary>
    /// Zorunlu revision mi?
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Kriter aktif mi?
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Audit fields - EntityBaseModel'de yok, manuel ekliyoruz
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedByUserId { get; set; }

    // Navigation properties
    public virtual SubmissionFeedbackEntity? SubmissionFeedback { get; set; }
    public virtual FormCriterionLinkEntity? CriterionLink { get; set; }
}

/// <summary>
/// Feedback notification log entity - PostgreSQL için
/// </summary>
public class FeedbackNotificationLogEntity : EntityBaseModel
{
    /// <summary>
    /// Feedback ID'si
    /// </summary>
    [Required]
    public required string SubmissionFeedbackId { get; set; }

    /// <summary>
    /// Notification türü (Email, SMS, InApp)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string NotificationType { get; set; }

    /// <summary>
    /// Alıcı kullanıcı ID'si
    /// </summary>
    [Required]
    public required string RecipientUserId { get; set; }

    /// <summary>
    /// Notification başlığı
    /// </summary>
    [Required]
    [StringLength(200)]
    public required string Subject { get; set; }

    /// <summary>
    /// Notification içeriği
    /// </summary>
    [Required]
    [StringLength(2000)]
    public required string Content { get; set; }

    /// <summary>
    /// Gönderim durumu (Pending, Sent, Failed)
    /// </summary>
    [Required]
    [StringLength(20)]
    public required string Status { get; set; } = "Pending";

    /// <summary>
    /// Gönderim tarihi
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    [StringLength(1000)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Retry sayısı
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Son retry tarihi
    /// </summary>
    public DateTime? LastRetryAt { get; set; }

    // Audit fields - EntityBaseModel'de yok, manuel ekliyoruz
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedByUserId { get; set; }

    // Navigation properties
    public virtual SubmissionFeedbackEntity? SubmissionFeedback { get; set; }
}
