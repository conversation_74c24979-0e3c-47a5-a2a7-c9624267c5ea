using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Feedback history DTO'su - Submission için tüm feedback geçmişi
    /// </summary>
    public class FeedbackHistoryDto
    {
        /// <summary>
        /// Submission ID'si
        /// </summary>
        public string SubmissionId { get; set; } = string.Empty;

        /// <summary>
        /// Akademisyen bilgileri
        /// </summary>
        public string AcademicianName { get; set; } = string.Empty;

        /// <summary>
        /// Form adı
        /// </summary>
        public string FormName { get; set; } = string.Empty;

        /// <summary>
        /// Feedback kayıtları (kronolojik sırada)
        /// </summary>
        public List<FeedbackEntryDto> FeedbackEntries { get; set; } = new();

        /// <summary>
        /// Mevcut submission durumu
        /// </summary>
        public string CurrentStatus { get; set; } = string.Empty;

        /// <summary>
        /// Son feedback tarihi
        /// </summary>
        public DateTime? LastFeedbackDate { get; set; }
    }

    /// <summary>
    /// Tekil feedback entry DTO'su
    /// </summary>
    public class FeedbackEntryDto
    {
        /// <summary>
        /// Feedback ID'si
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Feedback türü (Approval, Rejection, RevisionRequest)
        /// </summary>
        public string FeedbackType { get; set; } = string.Empty;

        /// <summary>
        /// Controller kullanıcı ID'si
        /// </summary>
        public string ControllerUserId { get; set; } = string.Empty;

        /// <summary>
        /// Controller adı
        /// </summary>
        public string ControllerName { get; set; } = string.Empty;

        /// <summary>
        /// Feedback mesajı/yorumu
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Feedback tarihi
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Submission'ın o andaki durumu
        /// </summary>
        public string SubmissionStatusAtTime { get; set; } = string.Empty;

        /// <summary>
        /// Belirli kriterler için feedback (opsiyonel)
        /// </summary>
        public List<CriterionFeedbackDto>? CriterionFeedbacks { get; set; }
    }

    /// <summary>
    /// Kriter bazında feedback DTO'su
    /// </summary>
    public class CriterionFeedbackDto
    {
        /// <summary>
        /// Kriter ID'si
        /// </summary>
        public string CriterionId { get; set; } = string.Empty;

        /// <summary>
        /// Kriter adı
        /// </summary>
        public string CriterionName { get; set; } = string.Empty;

        /// <summary>
        /// Kriter için feedback mesajı
        /// </summary>
        public string FeedbackMessage { get; set; } = string.Empty;

        /// <summary>
        /// Kriter durumu (Approved, NeedsRevision, Rejected)
        /// </summary>
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Revision request DTO'su - Düzeltme talebi için
    /// </summary>
    public class RevisionRequestDto
    {
        /// <summary>
        /// Submission ID'si
        /// </summary>
        [Required]
        public string SubmissionId { get; set; } = string.Empty;

        /// <summary>
        /// Genel revision mesajı
        /// </summary>
        [Required]
        [StringLength(2000, MinimumLength = 10)]
        public string GeneralMessage { get; set; } = string.Empty;

        /// <summary>
        /// Kriter bazında revision talepleri
        /// </summary>
        public List<CriterionRevisionRequestDto> CriterionRevisions { get; set; } = new();

        /// <summary>
        /// Revision deadline (opsiyonel)
        /// </summary>
        public DateTime? RevisionDeadline { get; set; }

        /// <summary>
        /// Öncelik seviyesi (Low, Medium, High)
        /// </summary>
        public string Priority { get; set; } = "Medium";
    }

    /// <summary>
    /// Kriter bazında revision request DTO'su
    /// </summary>
    public class CriterionRevisionRequestDto
    {
        /// <summary>
        /// Kriter ID'si
        /// </summary>
        [Required]
        public string CriterionId { get; set; } = string.Empty;

        /// <summary>
        /// Revision mesajı
        /// </summary>
        [Required]
        [StringLength(1000, MinimumLength = 5)]
        public string RevisionMessage { get; set; } = string.Empty;

        /// <summary>
        /// Revision türü (DataCorrection, EvidenceUpdate, AdditionalInfo)
        /// </summary>
        public string RevisionType { get; set; } = "DataCorrection";

        /// <summary>
        /// Zorunlu mu?
        /// </summary>
        public bool IsRequired { get; set; } = true;
    }

    /// <summary>
    /// Enhanced approval feedback DTO'su - Mevcut SubmissionApprovalDto'yu genişletir
    /// </summary>
    public class ApprovalFeedbackDto
    {
        /// <summary>
        /// Submission ID'si
        /// </summary>
        [Required]
        public string SubmissionId { get; set; } = string.Empty;

        /// <summary>
        /// Onay yorumları (opsiyonel)
        /// </summary>
        [StringLength(2000)]
        public string? Comments { get; set; }

        /// <summary>
        /// Kriter bazında onay yorumları
        /// </summary>
        public List<CriterionApprovalDto> CriterionApprovals { get; set; } = new();

        /// <summary>
        /// Genel performans notu (1-10 arası)
        /// </summary>
        [Range(1, 10)]
        public int? OverallRating { get; set; }

        /// <summary>
        /// Öne çıkan başarılar
        /// </summary>
        [StringLength(1000)]
        public string? Highlights { get; set; }

        /// <summary>
        /// Gelecek için öneriler
        /// </summary>
        [StringLength(1000)]
        public string? FutureSuggestions { get; set; }
    }

    /// <summary>
    /// Kriter bazında onay DTO'su
    /// </summary>
    public class CriterionApprovalDto
    {
        /// <summary>
        /// Kriter ID'si
        /// </summary>
        [Required]
        public string CriterionId { get; set; } = string.Empty;

        /// <summary>
        /// Kriter için yorum
        /// </summary>
        [StringLength(500)]
        public string? Comment { get; set; }

        /// <summary>
        /// Kriter notu (1-10 arası)
        /// </summary>
        [Range(1, 10)]
        public int? Rating { get; set; }

        /// <summary>
        /// Kriter durumu (Excellent, Good, Satisfactory, NeedsImprovement)
        /// </summary>
        public string Status { get; set; } = "Good";
    }

    /// <summary>
    /// Feedback özet DTO'su - Dashboard için
    /// </summary>
    public class FeedbackSummaryDto
    {
        /// <summary>
        /// Toplam feedback sayısı
        /// </summary>
        public int TotalFeedbacks { get; set; }

        /// <summary>
        /// Onaylanan submission sayısı
        /// </summary>
        public int ApprovedCount { get; set; }

        /// <summary>
        /// Reddedilen submission sayısı
        /// </summary>
        public int RejectedCount { get; set; }

        /// <summary>
        /// Revision talep edilen sayısı
        /// </summary>
        public int RevisionRequestedCount { get; set; }

        /// <summary>
        /// Ortalama feedback süresi (saat)
        /// </summary>
        public double AverageFeedbackTimeHours { get; set; }

        /// <summary>
        /// Bu ay verilen feedback sayısı
        /// </summary>
        public int FeedbacksThisMonth { get; set; }

        /// <summary>
        /// Bekleyen feedback sayısı
        /// </summary>
        public int PendingFeedbacks { get; set; }
    }

    /// <summary>
    /// Feedback constants - Enum değerleri için
    /// </summary>
    public static class FeedbackConstants
    {
        public static class FeedbackTypes
        {
            public const string Approval = "Approval";
            public const string Rejection = "Rejection";
            public const string RevisionRequest = "RevisionRequest";
        }

        public static class RevisionTypes
        {
            public const string DataCorrection = "DataCorrection";
            public const string EvidenceUpdate = "EvidenceUpdate";
            public const string AdditionalInfo = "AdditionalInfo";
        }

        public static class Priority
        {
            public const string Low = "Low";
            public const string Medium = "Medium";
            public const string High = "High";
        }

        public static class CriterionStatus
        {
            public const string Approved = "Approved";
            public const string NeedsRevision = "NeedsRevision";
            public const string Rejected = "Rejected";
            public const string Excellent = "Excellent";
            public const string Good = "Good";
            public const string Satisfactory = "Satisfactory";
            public const string NeedsImprovement = "NeedsImprovement";
        }
    }
}
