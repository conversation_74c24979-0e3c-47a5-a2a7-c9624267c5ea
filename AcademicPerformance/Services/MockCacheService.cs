using Rlx.Shared.Interfaces;
namespace AcademicPerformance.Services
{
    /// <summary>
    /// Test için geçici mock cache service
    /// </summary>
    public class MockCacheService : IRlxCacheService
    {
        public Task<string?> GetAsync(string key)
        {
            return Task.FromResult<string?>(null);
        }
        public Task SetAsync(string key, string value, TimeSpan? expiry = null)
        {
            return Task.CompletedTask;
        }
        public Task RemoveAsync(string key)
        {
            return Task.CompletedTask;
        }
    }
}
