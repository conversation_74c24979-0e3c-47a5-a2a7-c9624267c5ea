using MongoDB.Driver;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Services.Interfaces;

public interface IMongoDbService
{
    // Genel MongoDB işlemleri
    Task<T?> GetDocumentAsync<T>(FilterDefinition<T> filter);
    Task<List<T>> GetDocumentsAsync<T>(FilterDefinition<T> filter, SortDefinition<T>? sort = null);
    Task InsertDocumentAsync<T>(T document);
    Task<ReplaceOneResult> ReplaceDocumentAsync<T>(FilterDefinition<T> filter, T document);
    Task<UpdateResult> UpdateDocumentAsync<T>(FilterDefinition<T> filter, UpdateDefinition<T> update, UpdateOptions? options = null);
    Task<DeleteResult> DeleteDocumentAsync<T>(FilterDefinition<T> filter);
    Task<long> CountDocumentsAsync<T>(FilterDefinition<T> filter);

    // <PERSON>zel işlemler
    Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync();
    Task<DynamicCriterionTemplate?> GetDynamicCriterionTemplateByIdAsync(string id);
    Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesByStatusAsync(string status);
    Task<DynamicCriterionTemplate> CreateDynamicCriterionTemplateAsync(DynamicCriterionTemplate template);
    Task<bool> UpdateDynamicCriterionTemplateAsync(string id, DynamicCriterionTemplate template);
    Task<bool> DeleteDynamicCriterionTemplateAsync(string id);

    // Pagination metodları
    Task<PagedListDto<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync(PagedListCo<GetDynamicCriterionTemplatesCo> co);

    Task<List<SubmittedDynamicDataDoc>> GetSubmittedPerformanceDataAsync();
    Task<SubmittedDynamicDataDoc?> GetSubmittedPerformanceDataByIdAsync(string id);
    Task<List<SubmittedDynamicDataDoc>> GetSubmittedPerformanceDataBySubmissionIdAsync(string academicSubmissionId);
    Task<List<SubmittedDynamicDataDoc>> GetSubmittedPerformanceDataByUserIdAsync(string academicianUserId);
    Task<SubmittedDynamicDataDoc> CreateSubmittedPerformanceDataAsync(SubmittedDynamicDataDoc data);
    Task<bool> UpdateSubmittedPerformanceDataAsync(string id, SubmittedDynamicDataDoc data);
    Task<bool> DeleteSubmittedPerformanceDataAsync(string id);
}
