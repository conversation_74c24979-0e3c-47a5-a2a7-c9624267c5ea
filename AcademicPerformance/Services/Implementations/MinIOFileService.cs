using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Services.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace AcademicPerformance.Services.Implementations;

/// <summary>
/// MinIO file operations service implementation
/// </summary>
public class MinIOFileService : IMinIOFileService
{
    private readonly IMinioClient _minioClient;
    private readonly MinIOConfiguration _config;
    private readonly ILogger<MinIOFileService> _logger;

    public MinIOFileService(
        IMinioClient minioClient,
        IOptions<MinIOConfiguration> config,
        ILogger<MinIOFileService> logger)
    {
        _minioClient = minioClient;
        _config = config.Value;
        _logger = logger;
    }

    public async Task<MinIOUploadResult> UploadFileAsync(
        string bucketName,
        string objectName,
        Stream stream,
        string contentType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("MinIO dosya yükleme başlatılıyor: {BucketName}/{ObjectName}", bucketName, objectName);

            // Bucket var mı kontrol et
            await EnsureBucketExistsAsync(bucketName, cancellationToken);

            // Stream pozisyonunu sıfırla
            if (stream.CanSeek)
                stream.Position = 0;

            var putObjectArgs = new PutObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithStreamData(stream)
                .WithObjectSize(stream.Length)
                .WithContentType(contentType);

            var response = await _minioClient.PutObjectAsync(putObjectArgs, cancellationToken);

            _logger.LogInformation("MinIO dosya yükleme başarılı: {BucketName}/{ObjectName}, ETag: {ETag}",
                bucketName, objectName, response.Etag);

            return new MinIOUploadResult
            {
                Success = true,
                ObjectName = objectName,
                ETag = response.Etag,
                Size = stream.Length
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO dosya yükleme hatası: {BucketName}/{ObjectName}", bucketName, objectName);

            return new MinIOUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<MinIOUploadResult> UploadFileAsync(
        string bucketName,
        string objectName,
        byte[] data,
        string contentType,
        CancellationToken cancellationToken = default)
    {
        using var stream = new MemoryStream(data);
        return await UploadFileAsync(bucketName, objectName, stream, contentType, cancellationToken);
    }

    public async Task<Stream> DownloadFileAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("MinIO dosya indirme başlatılıyor: {BucketName}/{ObjectName}", bucketName, objectName);

            var memoryStream = new MemoryStream();

            var getObjectArgs = new GetObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithCallbackStream(stream => stream.CopyTo(memoryStream));

            await _minioClient.GetObjectAsync(getObjectArgs, cancellationToken);

            memoryStream.Position = 0;

            _logger.LogInformation("MinIO dosya indirme başarılı: {BucketName}/{ObjectName}, Size: {Size}",
                bucketName, objectName, memoryStream.Length);

            return memoryStream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO dosya indirme hatası: {BucketName}/{ObjectName}", bucketName, objectName);
            throw;
        }
    }

    public async Task<byte[]> DownloadFileBytesAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default)
    {
        using var stream = await DownloadFileAsync(bucketName, objectName, cancellationToken);
        return ((MemoryStream)stream).ToArray();
    }

    public async Task<bool> DeleteFileAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("MinIO dosya silme başlatılıyor: {BucketName}/{ObjectName}", bucketName, objectName);

            var removeObjectArgs = new RemoveObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName);

            await _minioClient.RemoveObjectAsync(removeObjectArgs, cancellationToken);

            _logger.LogInformation("MinIO dosya silme başarılı: {BucketName}/{ObjectName}", bucketName, objectName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO dosya silme hatası: {BucketName}/{ObjectName}", bucketName, objectName);
            return false;
        }
    }

    public async Task<bool> FileExistsAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statObjectArgs = new StatObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName);

            await _minioClient.StatObjectAsync(statObjectArgs, cancellationToken);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<MinIOFileMetadata?> GetFileMetadataAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statObjectArgs = new StatObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName);

            var objectStat = await _minioClient.StatObjectAsync(statObjectArgs, cancellationToken);

            return new MinIOFileMetadata
            {
                ObjectName = objectName,
                Size = objectStat.Size,
                ContentType = objectStat.ContentType,
                ETag = objectStat.ETag,
                LastModified = objectStat.LastModified,
                UserMetadata = objectStat.MetaData?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO dosya metadata alma hatası: {BucketName}/{ObjectName}", bucketName, objectName);
            return null;
        }
    }

    public async Task<string> GeneratePresignedUrlAsync(
        string bucketName,
        string objectName,
        int expirySeconds,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var presignedGetObjectArgs = new PresignedGetObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithExpiry(expirySeconds);

            var url = await _minioClient.PresignedGetObjectAsync(presignedGetObjectArgs);

            _logger.LogInformation("MinIO presigned URL oluşturuldu: {BucketName}/{ObjectName}, Expiry: {Expiry}s",
                bucketName, objectName, expirySeconds);

            return url;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO presigned URL oluşturma hatası: {BucketName}/{ObjectName}", bucketName, objectName);
            throw;
        }
    }

    public async Task<List<MinIOFileInfo>> ListFilesAsync(
        string bucketName,
        string? prefix = null,
        bool recursive = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var files = new List<MinIOFileInfo>();

            var listObjectsArgs = new ListObjectsArgs()
                .WithBucket(bucketName)
                .WithRecursive(recursive);

            if (!string.IsNullOrEmpty(prefix))
                listObjectsArgs = listObjectsArgs.WithPrefix(prefix);

            var observable = _minioClient.ListObjectsEnumAsync(listObjectsArgs, cancellationToken);

            await foreach (var item in observable.WithCancellation(cancellationToken))
            {
                files.Add(new MinIOFileInfo
                {
                    ObjectName = item.Key,
                    Size = (long)item.Size,
                    LastModified = DateTime.Parse(item.LastModified),
                    ETag = item.ETag,
                    IsDirectory = item.IsDir
                });
            }

            _logger.LogInformation("MinIO dosya listesi alındı: {BucketName}, Count: {Count}", bucketName, files.Count);
            return files;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO dosya listesi alma hatası: {BucketName}", bucketName);
            throw;
        }
    }

    public async Task<bool> BucketExistsAsync(
        string bucketName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var bucketExistsArgs = new BucketExistsArgs().WithBucket(bucketName);
            return await _minioClient.BucketExistsAsync(bucketExistsArgs, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO bucket kontrol hatası: {BucketName}", bucketName);
            return false;
        }
    }

    public async Task<bool> CreateBucketAsync(
        string bucketName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var bucketExists = await BucketExistsAsync(bucketName, cancellationToken);
            if (bucketExists)
                return true;

            var makeBucketArgs = new MakeBucketArgs().WithBucket(bucketName);
            await _minioClient.MakeBucketAsync(makeBucketArgs, cancellationToken);

            _logger.LogInformation("MinIO bucket oluşturuldu: {BucketName}", bucketName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MinIO bucket oluşturma hatası: {BucketName}", bucketName);
            return false;
        }
    }

    public async Task<MinIOValidationResult> ValidateFileAsync(
        Stream stream,
        string fileName,
        string contentType)
    {
        var result = new MinIOValidationResult();

        try
        {
            // Dosya boyutu kontrolü
            result.FileSize = stream.Length;
            if (stream.Length > _config.MaxFileSize)
            {
                result.Errors.Add($"Dosya boyutu çok büyük. Maksimum: {_config.MaxFileSize / (1024 * 1024)}MB");
            }

            if (stream.Length == 0)
            {
                result.Errors.Add("Dosya boş olamaz");
            }

            // Dosya uzantısı kontrolü
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            if (!_config.IsExtensionAllowed(extension))
            {
                result.Errors.Add($"Dosya uzantısı desteklenmiyor: {extension}");
            }

            // MIME type kontrolü
            if (!_config.IsMimeTypeAllowed(contentType))
            {
                result.Errors.Add($"Dosya türü desteklenmiyor: {contentType}");
            }

            result.DetectedContentType = contentType;
            result.IsValid = result.Errors.Count == 0;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dosya validation hatası: {FileName}", fileName);
            result.Errors.Add($"Validation hatası: {ex.Message}");
            result.IsValid = false;
            return result;
        }
    }

    public string GenerateUniqueObjectName(string originalFileName, string? prefix = null)
    {
        var extension = Path.GetExtension(originalFileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);

        // Güvenli dosya adı oluştur
        var safeName = string.Join("_", nameWithoutExtension.Split(Path.GetInvalidFileNameChars()));

        // Unique identifier ekle
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        var timestamp = DateTimeOffset.UtcNow.ToString("yyyyMMdd_HHmmss");

        var uniqueFileName = $"{safeName}_{timestamp}_{uniqueId}{extension}";

        if (!string.IsNullOrEmpty(prefix))
        {
            return $"{prefix.TrimEnd('/')}/{uniqueFileName}";
        }

        return uniqueFileName;
    }

    private async Task EnsureBucketExistsAsync(string bucketName, CancellationToken cancellationToken)
    {
        var exists = await BucketExistsAsync(bucketName, cancellationToken);
        if (!exists)
        {
            await CreateBucketAsync(bucketName, cancellationToken);
        }
    }

    /// <summary>
    /// Copy file from one location to another within MinIO
    /// </summary>
    public async Task<MinIOUploadResult> CopyFileAsync(string sourceBucket, string sourceObject, string destBucket, string destObject)
    {
        try
        {
            var copyArgs = new CopyObjectArgs()
                .WithBucket(destBucket)
                .WithObject(destObject)
                .WithCopyObjectSource(new CopySourceObjectArgs()
                    .WithBucket(sourceBucket)
                    .WithObject(sourceObject));

            await _minioClient.CopyObjectAsync(copyArgs);

            // Get the ETag of the copied object
            var statArgs = new StatObjectArgs()
                .WithBucket(destBucket)
                .WithObject(destObject);

            var stat = await _minioClient.StatObjectAsync(statArgs);

            return new MinIOUploadResult
            {
                Success = true,
                ObjectName = destObject,
                ETag = stat.ETag,
                Size = stat.Size
            };
        }
        catch (Exception ex)
        {
            return new MinIOUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Get file metadata from MinIO
    /// </summary>
    public async Task<MinIOFileMetadata> GetFileMetadataAsync(string bucketName, string objectName)
    {
        try
        {
            var statArgs = new StatObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName);

            var stat = await _minioClient.StatObjectAsync(statArgs);

            return new MinIOFileMetadata
            {
                ObjectName = objectName,
                FileName = Path.GetFileName(objectName),
                Size = stat.Size,
                ContentType = stat.ContentType,
                ETag = stat.ETag,
                LastModified = stat.LastModified,
                Checksum = stat.ETag?.Replace("\"", "") // ETag is often the MD5 hash
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to get file metadata: {ex.Message}", ex);
        }
    }

}
