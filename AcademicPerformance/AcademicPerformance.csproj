<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.15" />

    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Localization" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.2" />
    <PackageReference Include="Minio" Version="6.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
    <PackageReference Include="OpenIddict.Validation.AspNetCore" Version="6.3.0" />
    <PackageReference Include="OpenIddict.Validation.SystemNetHttp" Version="6.3.0" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="RabbitMQ.Client" Version="7.0.0" />
    <ProjectReference Include="../Rlx.Shared/Rlx.Shared.csproj" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.24" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
    <PackageReference Include="MongoDB.Driver" Version="3.2.1" />
  </ItemGroup>
</Project>