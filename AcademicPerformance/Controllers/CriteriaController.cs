using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;

using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Consts;
namespace AcademicPerformance.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class CriteriaController : BaseApiController
    {
        private readonly ICriteriaManager _criteriaManager;
        private readonly IUserContextHelper _userContextHelper;
        private readonly IRlxSystemLogHelper<CriteriaController> _systemLogHelper;
        private readonly ILogger<CriteriaController> _logger;
        public CriteriaController(
            ICriteriaManager criteriaManager,
            IUserContextHelper userContextHelper,
            IRlxSystemLogHelper<CriteriaController> systemLogHelper,
            ILogger<CriteriaController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _criteriaManager = criteriaManager;
            _userContextHelper = userContextHelper;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
        }
        #region Test Localization
        /// Test localization functionality
        [HttpGet]
        [Authorize($"{APConsts.PermissionActionAP}.test")]
        public IActionResult TestLocalization()
        {
            var testMessage = _localizer["Test"];
            var welcomeMessage = _localizer["Welcome"];
            var successMessage = _localizer["Success"];
            var errorMessage = _localizer["Error"];
            var result = new
            {
                test = testMessage.Value,
                welcome = welcomeMessage.Value,
                success = successMessage.Value,
                error = errorMessage.Value,
                culture = System.Globalization.CultureInfo.CurrentCulture.Name,
                uiCulture = System.Globalization.CultureInfo.CurrentUICulture.Name
            };
            return SuccessResponse(result);
        }
        #endregion
        #region Dynamic Criteria Management
        /// <summary>
        /// Tüm dinamik kriter şablonlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış dinamik kriter şablonları listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetDynamicTemplates([FromQuery] PagedListCo<GetDynamicCriterionTemplatesCo> co)
        {
            try
            {
                var pagedTemplates = await _criteriaManager.GetDynamicCriterionTemplatesAsync(co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedTemplates.Count} dynamic criterion templates (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedTemplates, _localizer["DynamicCriterionTemplatesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving dynamic criterion templates", ex);
                _logger.LogError(ex, "Error retrieving dynamic criterion templates");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }

        /// <summary>
        /// Sayfalanmış dinamik kriter şablonları arama - Gelişmiş filtreleme ile
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> SearchDynamicTemplates([FromBody] PagedListCo<GetDynamicCriterionTemplatesCo> co)
        {
            try
            {
                var pagedTemplates = await _criteriaManager.GetDynamicCriterionTemplatesAsync(co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedTemplates.Count} dynamic criterion templates (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedTemplates, _localizer["DynamicCriterionTemplatesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error searching dynamic criterion templates", ex);
                _logger.LogError(ex, "Error searching dynamic criterion templates");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// ID'ye göre dinamik kriter şablonunu getir
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetDynamicTemplate(string id)
        {
            try
            {
                var template = await _criteriaManager.GetDynamicCriterionTemplateByIdAsync(id);
                if (template == null)
                {
                    return NotFoundResponse(_localizer["DynamicCriterionTemplateNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Retrieved dynamic criterion template: {id}");
                return SuccessResponse(template);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving dynamic criterion template: {id}", ex);
                _logger.LogError(ex, "Error retrieving dynamic criterion template: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// <summary>
        /// Duruma göre dinamik kriter şablonlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="status">Şablon durumu (Draft, Active, Inactive)</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış dinamik kriter şablonları listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ManageCriteria)]
        public async Task<IActionResult> GetDynamicTemplatesByStatus(string status, [FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var pagedTemplates = await _criteriaManager.GetDynamicCriterionTemplatesByStatusAsync(status, co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedTemplates.Count} dynamic criterion templates with status: {status} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedTemplates, _localizer["DynamicCriterionTemplatesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving dynamic criterion templates by status: {status}", ex);
                _logger.LogError(ex, "Error retrieving dynamic criterion templates by status: {Status}", status);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// <summary>
        /// Aktif dinamik kriter şablonlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış aktif dinamik kriter şablonları listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ManageCriteria)]
        public async Task<IActionResult> GetActiveDynamicTemplates([FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var pagedTemplates = await _criteriaManager.GetActiveDynamicCriterionTemplatesAsync(co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedTemplates.Count} active dynamic criterion templates (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedTemplates, _localizer["DynamicCriterionTemplatesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving active dynamic criterion templates", ex);
                _logger.LogError(ex, "Error retrieving active dynamic criterion templates");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Yeni bir dinamik kriter şablonu oluştur
        [HttpPost]
        [Authorize(APConsts.Policies.ManageCriteria)]
        public async Task<IActionResult> AddDynamicTemplate([FromBody] DynamicCriterionTemplateCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId();
                var createdTemplate = await _criteriaManager.CreateDynamicCriterionTemplateAsync(dto, userId);
                await _systemLogHelper.LogInfoAsync($"Created dynamic criterion template: {createdTemplate.Id}");
                return SuccessResponse(createdTemplate, _localizer["DynamicCriterionTemplateCreatedSuccessfully"].Value);
            }
            catch (ArgumentException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Invalid dynamic criterion template data: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error creating dynamic criterion template", ex);
                _logger.LogError(ex, "Error creating dynamic criterion template");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir dinamik kriter şablonunu güncelle
        [HttpPut]
        [Authorize(Policy = "RequireAdminRole")]
        public async Task<IActionResult> UpdateDynamicTemplate(string id, [FromBody] DynamicCriterionTemplateUpdateDto dto)
        {
            try
            {
                if (id != dto.Id)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var userId = _userContextHelper.GetUserId();
                var success = await _criteriaManager.UpdateDynamicCriterionTemplateAsync(dto, userId);
                if (!success)
                {
                    return NotFoundResponse(_localizer["DynamicCriterionTemplateNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated dynamic criterion template: {id}");
                return SuccessResponse(_localizer["DynamicCriterionTemplateUpdatedSuccessfully"].Value);
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Cannot update dynamic criterion template {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating dynamic criterion template: {id}", ex);
                _logger.LogError(ex, "Error updating dynamic criterion template: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Dinamik kriter şablonu durumunu güncelle
        [HttpPatch]
        [Authorize(Policy = "RequireAdminRole")]
        public async Task<IActionResult> UpdateDynamicTemplateStatus(string id, [FromBody] CriterionStatusUpdateDto dto)
        {
            try
            {
                if (id != dto.Id)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var userId = _userContextHelper.GetUserId();
                var success = await _criteriaManager.UpdateDynamicCriterionTemplateStatusAsync(id, dto.Status, userId);
                if (!success)
                {
                    return NotFoundResponse(_localizer["DynamicCriterionTemplateNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated dynamic criterion template status: {id} -> {dto.Status}");
                return Ok(new { message = _localizer["DynamicCriterionTemplateStatusUpdatedSuccessfully"].Value });
            }
            catch (ArgumentException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Invalid status for dynamic criterion template {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating dynamic criterion template status: {id}", ex);
                _logger.LogError(ex, "Error updating dynamic criterion template status: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir dinamik kriter şablonunu sil
        [HttpDelete]
        [Authorize(Policy = "RequireAdminRole")]
        public async Task<IActionResult> DeleteDynamicTemplate(string id)
        {
            try
            {
                var success = await _criteriaManager.DeleteDynamicCriterionTemplateAsync(id);
                if (!success)
                {
                    return NotFoundResponse(_localizer["DynamicCriterionTemplateNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Deleted dynamic criterion template: {id}");
                return Ok(new { message = _localizer["DynamicCriterionTemplateDeletedSuccessfully"].Value });
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Cannot delete dynamic criterion template {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error deleting dynamic criterion template: {id}", ex);
                _logger.LogError(ex, "Error deleting dynamic criterion template: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        #endregion
        #region Static Criteria Management
        /// <summary>
        /// Tüm statik kriter tanımlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış statik kriter tanımları listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetStaticCriterion([FromQuery] PagedListCo<GetStaticCriterionDefinitionsCo> co)
        {
            try
            {
                var pagedDefinitions = await _criteriaManager.GetStaticCriterionDefinitionsAsync(co, co.Criteria?.Culture ?? "en");
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedDefinitions.Count} static criterion definitions (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedDefinitions, _localizer["StaticCriterionDefinitionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving static criterion definitions", ex);
                _logger.LogError(ex, "Error retrieving static criterion definitions");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }

        /// <summary>
        /// Sayfalanmış statik kriter tanımları arama - Gelişmiş filtreleme ile
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> SearchStaticCriterion([FromBody] PagedListCo<GetStaticCriterionDefinitionsCo> co, [FromQuery] string? culture = "en")
        {
            try
            {
                var pagedDefinitions = await _criteriaManager.GetStaticCriterionDefinitionsAsync(co, culture);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedDefinitions.Count} static criterion definitions (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedDefinitions, _localizer["StaticCriterionDefinitionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error searching static criterion definitions", ex);
                _logger.LogError(ex, "Error searching static criterion definitions");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }

        /// ID'ye göre statik kriter tanımını getir
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetStaticCriterionById(string staticCriterionSystemId)
        {
            try
            {
                var definition = await _criteriaManager.GetStaticCriterionDefinitionByIdAsync(staticCriterionSystemId);
                if (definition == null)
                {
                    return NotFoundResponse(_localizer["StaticCriterionDefinitionNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Retrieved static criterion definition: {staticCriterionSystemId}");
                return SuccessResponse(definition);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving static criterion definition: {staticCriterionSystemId}", ex);
                _logger.LogError(ex, "Error retrieving static criterion definition: {Id}", staticCriterionSystemId);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// <summary>
        /// Aktif statik kriter tanımlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış aktif statik kriter tanımları listesi</returns>
        [HttpGet]
        [Authorize(Policy = "RequireAdminRole")]
        public async Task<IActionResult> GetActiveStaticCriterion([FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var pagedDefinitions = await _criteriaManager.GetActiveStaticCriterionDefinitionsAsync(co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedDefinitions.Count} active static criterion definitions (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedDefinitions, _localizer["StaticCriterionDefinitionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving active static criterion definitions", ex);
                _logger.LogError(ex, "Error retrieving active static criterion definitions");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Statik kriter tanımı durumunu güncelle
        [HttpPatch]
        [Authorize(Policy = "RequireAdminRole")]
        public async Task<IActionResult> UpdateStaticCriterionStatus(string staticCriterionSystemId, [FromBody] StaticCriterionDefinitionUpdateDto dto)
        {
            try
            {
                if (staticCriterionSystemId != dto.StaticCriterionSystemId)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var success = await _criteriaManager.UpdateStaticCriterionDefinitionStatusAsync(dto);
                if (!success)
                {
                    return NotFoundResponse(_localizer["StaticCriterionDefinitionNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated static criterion definition status: {staticCriterionSystemId} -> {dto.IsActive}");
                return Ok(new { message = _localizer["StaticCriterionDefinitionStatusUpdatedSuccessfully"].Value });
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating static criterion definition status: {staticCriterionSystemId}", ex);
                _logger.LogError(ex, "Error updating static criterion definition status: {Id}", staticCriterionSystemId);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        #endregion
    }
}
