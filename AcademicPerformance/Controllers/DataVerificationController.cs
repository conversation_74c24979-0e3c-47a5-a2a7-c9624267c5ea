using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Consts;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Interfaces;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Resources;

namespace AcademicPerformance.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class DataVerificationController : BaseApiController
{
    private readonly AcademicPerformanceDbContext _dbContext;
    private readonly IControllerManager _controllerManager;
    private readonly IFeedbackManager _feedbackManager;
    private readonly IUserContextHelper _userContextHelper;
    private readonly IRlxSystemLogHelper<DataVerificationController> _systemLogHelper;
    private readonly ILogger<DataVerificationController> _logger;

    public DataVerificationController(
        AcademicPerformanceDbContext dbContext,
        IControllerManager controllerManager,
        IFeedbackManager feedbackManager,
        IUserContextHelper userContextHelper,
        IStringLocalizer<SharedResource> localizer,
        IRlxSystemLogHelper<DataVerificationController> systemLogHelper,
        ILogger<DataVerificationController> logger) : base(localizer)
    {
        _dbContext = dbContext;
        _controllerManager = controllerManager;
        _feedbackManager = feedbackManager;
        _userContextHelper = userContextHelper;
        _systemLogHelper = systemLogHelper;
        _logger = logger;
    }

    #region Dashboard Operations

    /// <returns>Controller dashboard verileri</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.ViewReports)]
    public async Task<IActionResult> GetControllerDashboard()
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Controller dashboard istendi: {controllerId}");

            var dashboard = await _controllerManager.GetControllerDashboardAsync(controllerId);

            await _systemLogHelper.LogInfoAsync($"Controller dashboard başarıyla getirildi: {controllerId}");
            return SuccessResponse(dashboard, _localizer["ControllerDashboardRetrievedSuccessfully"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Controller dashboard getirme hatası");
            return HandleException(ex, _localizer["ErrorRetrievingControllerDashboard"].Value);
        }
    }

    /// <summary>
    /// Controller istatistiklerini getir
    /// </summary>
    /// <returns>Controller statistics</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.ViewReports)]
    public async Task<IActionResult> GetControllerStatistics()
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Controller statistics istendi: {controllerId}");

            var statistics = await _controllerManager.GetControllerStatisticsAsync(controllerId);

            await _systemLogHelper.LogInfoAsync($"Controller statistics başarıyla getirildi: {controllerId}");
            return SuccessResponse(statistics, _localizer["ControllerStatisticsRetrievedSuccessfully"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Controller statistics getirme hatası");
            return HandleException(ex, _localizer["ErrorRetrievingControllerStatistics"].Value);
        }
    }

    #endregion

    #region Submission Review Operations


    /// <param name="submissionId">Submission ID'si</param>
    /// <returns>Detaylı submission review verileri</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> GetSubmissionForReview(string submissionId)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Submission review istendi: {submissionId}, Controller: {controllerId}");

            var submissionReview = await _controllerManager.GetSubmissionForReviewAsync(submissionId, controllerId);

            await _systemLogHelper.LogInfoAsync($"Submission review başarıyla getirildi: {submissionId}");
            return SuccessResponse(submissionReview, _localizer["SubmissionReviewRetrievedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Submission review unauthorized access: {SubmissionId}", submissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Submission review not found: {SubmissionId}", submissionId);
            return NotFoundResponse(_localizer["SubmissionNotFound"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Submission review getirme hatası: {SubmissionId}", submissionId);
            return HandleException(ex, _localizer["ErrorRetrievingSubmissionReview"].Value);
        }
    }

    #endregion

    #region Enhanced Pending Submissions


    /// <param name="co">Pagination ve filtreleme parametreleri</param>
    /// <returns>Sayfalanmış pending submissions listesi</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> GetPendingSubmissions([FromBody] PagedListCo<SubmissionFilterCo> co)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Pending submissions istendi: Controller: {controllerId}, Page: {co.Pager.Page}, Size: {co.Pager.Size}");

            var pendingSubmissions = await _controllerManager.GetPendingSubmissionsAsync(controllerId, co);

            await _systemLogHelper.LogInfoAsync($"Pending submissions başarıyla getirildi: Controller: {controllerId}, Total: {pendingSubmissions.TotalCount}");
            return SuccessResponse(pendingSubmissions, _localizer["PendingSubmissionsRetrievedSuccessfully"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Pending submissions getirme hatası");
            return HandleException(ex, _localizer["ErrorRetrievingPendingSubmissions"].Value);
        }
    }

    #endregion

    #region Legacy Endpoints (Backward Compatibility)

    /// <summary>
    /// Get detailed submission information for verification - Legacy endpoint
    /// Deprecated: Use GetSubmissionForReview instead
    /// </summary>
    /// <param name="submissionId">Submission AutoIncrement ID</param>
    /// <returns>Detailed submission information</returns>
    [HttpGet]
    [Obsolete("Use GetSubmissionForReview instead")]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> GetSubmissionDetails(int submissionId)
    {
        try
        {
            _logger.LogInformation("Submission details alınıyor: {SubmissionId}", submissionId);

            var submission = await _dbContext.AcademicSubmissions
                .Include(s => s.EvaluationForm)
                .Include(s => s.AcademicianProfile)
                .Include(s => s.EvidenceFiles)
                .FirstOrDefaultAsync(s => s.AutoIncrementId == submissionId);

            if (submission == null)
            {
                return NotFound(new { status = "Error", message = "Submission bulunamadı" });
            }

            var submissionDetails = new
            {
                id = submission.Id,
                autoIncrementId = submission.AutoIncrementId,
                academicianUserId = submission.AcademicianUniveristyUserId,
                academicianProfile = submission.AcademicianProfile != null ? new
                {
                    name = submission.AcademicianProfile.Name,
                    surname = submission.AcademicianProfile.Surname,
                    fullName = submission.AcademicianProfile.FullName,
                    email = submission.AcademicianProfile.Email,
                    department = submission.AcademicianProfile.Department,
                    academicCadre = submission.AcademicianProfile.AcademicCadre,
                    title = submission.AcademicianProfile.Title
                } : null,
                evaluationForm = submission.EvaluationForm != null ? new
                {
                    id = submission.EvaluationForm.Id,
                    autoIncrementId = submission.EvaluationForm.AutoIncrementId,
                    name = submission.EvaluationForm.Name,
                    description = submission.EvaluationForm.Description,
                    status = submission.EvaluationForm.Status,
                    evaluationPeriodStartDate = submission.EvaluationForm.EvaluationPeriodStartDate,
                    evaluationPeriodEndDate = submission.EvaluationForm.EvaluationPeriodEndDate
                } : null,
                status = submission.Status,
                submittedAt = submission.SubmittedAt,
                createdAt = submission.CreatedAt,
                updatedAt = submission.UpdatedAt,
                evidenceFiles = submission.EvidenceFiles?.Select(f => new
                {
                    id = f.Id,
                    autoIncrementId = f.AutoIncrementId,
                    fileName = f.FileName,
                    originalFileName = f.OriginalFileName,
                    sizeBytes = f.SizeBytes,
                    contentType = f.ContentType,
                    uploadedAt = f.UploadedAt,
                    description = f.Description,
                    storageType = f.StorageType,
                    hasValidPresignedUrl = f.MinioPresignedUrlExpiry.HasValue && f.MinioPresignedUrlExpiry > DateTime.UtcNow
                }).ToList(),
                submittedDataCount = 0 // TODO: Add dynamic data count when available
            };

            return Ok(new
            {
                status = "Success",
                message = "Submission details alındı",
                submission = submissionDetails
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Submission details alma hatası: {SubmissionId}", submissionId);
            return StatusCode(500, new { status = "Error", message = "Submission details alma sırasında hata oluştu", error = ex.Message });
        }
    }

    #endregion

    #region Enhanced Approval/Rejection Operations

    /// <param name="dto">Approval DTO with submission ID and comments</param>
    /// <returns>Approval result</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> ApproveSubmission([FromBody] SubmissionApprovalDto dto)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Submission approval istendi: {dto.SubmissionId}, Controller: {controllerId}");

            var success = await _controllerManager.ApproveSubmissionAsync(dto.SubmissionId, controllerId, dto.Comments);

            if (!success)
            {
                return BadRequestResponse(_localizer["SubmissionCannotBeApproved"].Value);
            }

            await _systemLogHelper.LogInfoAsync($"Submission başarıyla onaylandı: {dto.SubmissionId}, Controller: {controllerId}");
            return SuccessResponse(new
            {
                submissionId = dto.SubmissionId,
                approvedAt = DateTime.UtcNow,
                comments = dto.Comments
            }, _localizer["SubmissionApprovedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Submission approval unauthorized: {SubmissionId}", dto.SubmissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Submission approval hatası: {SubmissionId}", dto.SubmissionId);
            return HandleException(ex, _localizer["ErrorApprovingSubmission"].Value);
        }
    }

    /// <param name="dto">Rejection DTO with submission ID and comments</param>
    /// <returns>Rejection result</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> RejectSubmission([FromBody] SubmissionRejectionDto dto)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Submission rejection istendi: {dto.SubmissionId}, Controller: {controllerId}");

            var success = await _controllerManager.RejectSubmissionAsync(dto.SubmissionId, controllerId, dto.Comments);

            if (!success)
            {
                return BadRequestResponse(_localizer["SubmissionCannotBeRejected"].Value);
            }

            await _systemLogHelper.LogInfoAsync($"Submission başarıyla reddedildi: {dto.SubmissionId}, Controller: {controllerId}");
            return SuccessResponse(new
            {
                submissionId = dto.SubmissionId,
                rejectedAt = DateTime.UtcNow,
                comments = dto.Comments
            }, _localizer["SubmissionRejectedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Submission rejection unauthorized: {SubmissionId}", dto.SubmissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Submission rejection hatası: {SubmissionId}", dto.SubmissionId);
            return HandleException(ex, _localizer["ErrorRejectingSubmission"].Value);
        }
    }

    #endregion

    #region Enhanced Feedback Operations

    /// <param name="feedbackDto">Approval feedback verileri</param>
    /// <returns>Approval feedback sonucu</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> CreateApprovalFeedback([FromBody] ApprovalFeedbackDto feedbackDto)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Enhanced approval feedback istendi: {feedbackDto.SubmissionId}, Controller: {controllerId}");

            var feedbackEntry = await _feedbackManager.CreateApprovalFeedbackAsync(feedbackDto, controllerId);

            await _systemLogHelper.LogInfoAsync($"Enhanced approval feedback başarıyla oluşturuldu: {feedbackEntry.Id}, Submission: {feedbackDto.SubmissionId}");
            return SuccessResponse(feedbackEntry, _localizer["ApprovalFeedbackCreatedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Enhanced approval feedback unauthorized: {SubmissionId}", feedbackDto.SubmissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Enhanced approval feedback invalid operation: {SubmissionId}", feedbackDto.SubmissionId);
            return BadRequestResponse(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Enhanced approval feedback hatası: {SubmissionId}", feedbackDto.SubmissionId);
            return HandleException(ex, _localizer["ErrorCreatingApprovalFeedback"].Value);
        }
    }

    /// <param name="revisionDto">Revision request verileri</param>
    /// <returns>Revision request sonucu</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> CreateRevisionRequest([FromBody] RevisionRequestDto revisionDto)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Revision request istendi: {revisionDto.SubmissionId}, Controller: {controllerId}");

            var feedbackEntry = await _feedbackManager.CreateRevisionRequestAsync(revisionDto, controllerId);

            await _systemLogHelper.LogInfoAsync($"Revision request başarıyla oluşturuldu: {feedbackEntry.Id}, Submission: {revisionDto.SubmissionId}");
            return SuccessResponse(feedbackEntry, _localizer["RevisionRequestCreatedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Revision request unauthorized: {SubmissionId}", revisionDto.SubmissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Revision request invalid operation: {SubmissionId}", revisionDto.SubmissionId);
            return BadRequestResponse(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Revision request hatası: {SubmissionId}", revisionDto.SubmissionId);
            return HandleException(ex, _localizer["ErrorCreatingRevisionRequest"].Value);
        }
    }

    /// <param name="dto">Rejection DTO</param>
    /// <returns>Rejection feedback sonucu</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.ApproveSubmissions)]
    public async Task<IActionResult> CreateRejectionFeedback([FromBody] SubmissionRejectionDto dto)
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Enhanced rejection feedback istendi: {dto.SubmissionId}, Controller: {controllerId}");

            var feedbackEntry = await _feedbackManager.CreateRejectionFeedbackAsync(dto.SubmissionId, dto.Comments, controllerId);

            await _systemLogHelper.LogInfoAsync($"Enhanced rejection feedback başarıyla oluşturuldu: {feedbackEntry.Id}, Submission: {dto.SubmissionId}");
            return SuccessResponse(feedbackEntry, _localizer["RejectionFeedbackCreatedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Enhanced rejection feedback unauthorized: {SubmissionId}", dto.SubmissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Enhanced rejection feedback invalid operation: {SubmissionId}", dto.SubmissionId);
            return BadRequestResponse(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Enhanced rejection feedback hatası: {SubmissionId}", dto.SubmissionId);
            return HandleException(ex, _localizer["ErrorCreatingRejectionFeedback"].Value);
        }
    }

    /// <param name="submissionId">Submission ID'si</param>
    /// <returns>Feedback geçmişi</returns>
    [HttpGet("{submissionId}")]
    [Authorize(APConsts.Policies.ViewReports)]
    public async Task<IActionResult> GetFeedbackHistory(string submissionId)
    {
        try
        {
            var userId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Feedback history istendi: {submissionId}, User: {userId}");

            var feedbackHistory = await _feedbackManager.GetFeedbackHistoryAsync(submissionId, userId);

            await _systemLogHelper.LogInfoAsync($"Feedback history başarıyla getirildi: {submissionId}");
            return SuccessResponse(feedbackHistory, _localizer["FeedbackHistoryRetrievedSuccessfully"].Value);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Feedback history unauthorized: {SubmissionId}", submissionId);
            return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Feedback history invalid argument: {SubmissionId}", submissionId);
            return BadRequestResponse(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Feedback history hatası: {SubmissionId}", submissionId);
            return HandleException(ex, _localizer["ErrorRetrievingFeedbackHistory"].Value);
        }
    }


    /// <returns>Feedback istatistikleri</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.ViewReports)]
    public async Task<IActionResult> GetFeedbackStatistics()
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Feedback statistics istendi: Controller: {controllerId}");

            var statistics = await _feedbackManager.GetFeedbackStatisticsAsync(controllerId, "Controller");

            await _systemLogHelper.LogInfoAsync($"Feedback statistics başarıyla getirildi: Controller: {controllerId}");
            return SuccessResponse(statistics, _localizer["FeedbackStatisticsRetrievedSuccessfully"].Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Feedback statistics hatası");
            return HandleException(ex, _localizer["ErrorRetrievingFeedbackStatistics"].Value);
        }
    }

    #endregion

    #region Legacy Statistics (Backward Compatibility)


    /// <returns>Verification statistics</returns>
    [HttpGet]
    [Obsolete("Use GetControllerStatistics instead")]
    [Authorize(APConsts.Policies.ViewReports)]
    public async Task<IActionResult> GetVerificationStatistics()
    {
        try
        {
            var controllerId = _userContextHelper.GetUserId();
            await _systemLogHelper.LogInfoAsync($"Legacy verification statistics istendi: {controllerId}");

            var statistics = await _controllerManager.GetControllerStatisticsAsync(controllerId);

            // Legacy format için dönüştür
            var legacyStats = new
            {
                status = "Success",
                message = "Verification statistics alındı",
                statistics = new
                {
                    total = statistics.TotalReviewed,
                    pending = 0, // Legacy field - artık kullanılmıyor
                    approved = statistics.TotalApproved,
                    rejected = statistics.TotalRejected,
                    recentSubmissions = statistics.ReviewedThisWeek,
                    submissionsWithFiles = 0, // Legacy field - artık kullanılmıyor
                    approvalRate = statistics.ApprovalRate,
                    rejectionRate = statistics.TotalReviewed > 0 ?
                        Math.Round((double)statistics.TotalRejected / statistics.TotalReviewed * 100, 2) : 0
                }
            };

            await _systemLogHelper.LogInfoAsync($"Legacy verification statistics başarıyla getirildi: {controllerId}");
            return Ok(legacyStats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Legacy verification statistics alma hatası");
            return HandleException(ex, _localizer["ErrorRetrievingStatistics"].Value);
        }
    }

    #endregion
}
